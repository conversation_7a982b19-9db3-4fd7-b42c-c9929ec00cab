/**
 * 滤波器分析功能验证程序
 * 
 * 此文件包含用于验证滤波器分析算法正确性的测试函数
 * 可以在PC上编译运行，验证算法逻辑
 */

#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include <stdint.h>

#ifndef PI
#define PI 3.14159265358979f
#endif

#define SWEEP_BUFFER_SIZE 50

// 模拟STM32项目中的数据结构
typedef struct {
    float frequency;        
    float adc1_amplitude;   
    float adc2_amplitude;   
    float magnitude_db;     
    float phase_deg;        
} FrequencyResponse;

typedef struct {
    float center_frequency;     
    float cutoff_frequency;     
    float q_factor;             
    float bandwidth;            
    float max_gain_db;          
    float gain_at_cutoff_db;    
    uint8_t filter_type;        
} FilterCharacteristics;

// 全局变量
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];
uint16_t total_sweep_points = 0;

// 函数声明
void GenerateTestFilterData(float center_freq, float q_factor, uint8_t filter_type);
FilterCharacteristics AnalyzeFilterFromSweep(void);
float FindCutoffFrequency(float cutoff_level);
float CalculateQFactor(float center_freq, float bandwidth);
uint8_t DetermineFilterType(void);
void PrintFilterCharacteristics(FilterCharacteristics* filter_char);

/**
 * 生成测试滤波器数据
 */
void GenerateTestFilterData(float center_freq, float q_factor, uint8_t filter_type)
{
    printf("生成测试数据: 中心频率=%.1fHz, Q值=%.1f, 类型=%d\n", 
           center_freq, q_factor, filter_type);
    
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        float freq = 1000.0f + i * 400.0f;  // 1kHz到20.6kHz
        sweep_results[i].frequency = freq;
        
        float magnitude_db = 0;
        
        switch(filter_type) {
            case 0: // 低通
                {
                    float freq_ratio = freq / center_freq;
                    magnitude_db = -20.0f * log10f(sqrt(1.0f + pow(freq_ratio, 2)));
                }
                break;
                
            case 1: // 高通
                {
                    float freq_ratio = center_freq / freq;
                    magnitude_db = -20.0f * log10f(sqrt(1.0f + pow(freq_ratio, 2)));
                }
                break;
                
            case 2: // 带通
                {
                    float freq_ratio = freq / center_freq;
                    float q_term = q_factor * (freq_ratio - 1.0f / freq_ratio);
                    magnitude_db = -20.0f * log10f(sqrt(1.0f + q_term * q_term));
                }
                break;
                
            default:
                magnitude_db = 0;
                break;
        }
        
        sweep_results[i].magnitude_db = magnitude_db;
        sweep_results[i].phase_deg = 0;  // 简化
        sweep_results[i].adc1_amplitude = pow(10, magnitude_db/20.0f) * 1000.0f;
        sweep_results[i].adc2_amplitude = 1000.0f;
    }
    
    total_sweep_points = SWEEP_BUFFER_SIZE;
}

/**
 * 分析滤波器特性（与main.c中的函数相同）
 */
FilterCharacteristics AnalyzeFilterFromSweep(void)
{
    FilterCharacteristics filter_char = {0};
    
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    
    if (valid_points < 5) {
        filter_char.filter_type = 4; // 未知类型
        return filter_char;
    }
    
    // 找到最大增益和对应频率
    float max_magnitude = -100.0f;
    float max_freq = 0;
    
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
        }
    }
    
    filter_char.max_gain_db = max_magnitude;
    filter_char.center_frequency = max_freq;
    
    // 计算-3dB截止频率
    float cutoff_level = max_magnitude - 3.0f;
    filter_char.cutoff_frequency = FindCutoffFrequency(cutoff_level);
    filter_char.gain_at_cutoff_db = cutoff_level;
    
    // 计算带宽
    float lower_freq = 0, upper_freq = 0;
    int found_lower = 0, found_upper = 0;
    
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float gain_diff = fabs(sweep_results[i].magnitude_db - cutoff_level);
            
            if (gain_diff < 1.0f) {
                if (sweep_results[i].frequency < max_freq && !found_lower) {
                    lower_freq = sweep_results[i].frequency;
                    found_lower = 1;
                }
                if (sweep_results[i].frequency > max_freq && !found_upper) {
                    upper_freq = sweep_results[i].frequency;
                    found_upper = 1;
                }
            }
        }
    }
    
    if (found_lower && found_upper) {
        filter_char.bandwidth = upper_freq - lower_freq;
    } else if (found_lower || found_upper) {
        filter_char.bandwidth = fabs(max_freq - (found_lower ? lower_freq : upper_freq)) * 2;
    } else {
        filter_char.bandwidth = max_freq * 0.1f;
    }
    
    filter_char.q_factor = CalculateQFactor(filter_char.center_frequency, filter_char.bandwidth);
    filter_char.filter_type = DetermineFilterType();
    
    return filter_char;
}

float FindCutoffFrequency(float cutoff_level)
{
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    float closest_freq = 0;
    float min_diff = 1000.0f;
    
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float diff = fabs(sweep_results[i].magnitude_db - cutoff_level);
            if (diff < min_diff) {
                min_diff = diff;
                closest_freq = sweep_results[i].frequency;
            }
        }
    }
    
    return closest_freq;
}

float CalculateQFactor(float center_freq, float bandwidth)
{
    if (bandwidth > 0) {
        return center_freq / bandwidth;
    }
    return 0.0f;
}

uint8_t DetermineFilterType(void)
{
    // 简化的类型判断，基于频率响应
    return 2; // 假设为带通
}

void PrintFilterCharacteristics(FilterCharacteristics* filter_char)
{
    const char* filter_types[] = {"Low-Pass", "High-Pass", "Band-Pass", "Band-Stop", "Unknown"};
    
    printf("\n=== 滤波器分析结果 ===\n");
    printf("类型: %s\n", filter_types[filter_char->filter_type]);
    printf("中心频率: %.1f Hz\n", filter_char->center_frequency);
    printf("截止频率: %.1f Hz\n", filter_char->cutoff_frequency);
    printf("Q值: %.2f\n", filter_char->q_factor);
    printf("带宽: %.1f Hz\n", filter_char->bandwidth);
    printf("最大增益: %.1f dB\n", filter_char->max_gain_db);
    printf("=====================\n\n");
}

/**
 * 主测试函数
 */
int main()
{
    printf("滤波器分析算法验证程序\n");
    printf("========================\n\n");
    
    // 测试1: 带通滤波器
    printf("测试1: 带通滤波器\n");
    GenerateTestFilterData(10000.0f, 5.0f, 2);
    FilterCharacteristics result1 = AnalyzeFilterFromSweep();
    PrintFilterCharacteristics(&result1);
    
    // 测试2: 低通滤波器
    printf("测试2: 低通滤波器\n");
    GenerateTestFilterData(5000.0f, 1.0f, 0);
    FilterCharacteristics result2 = AnalyzeFilterFromSweep();
    PrintFilterCharacteristics(&result2);
    
    // 测试3: 高通滤波器
    printf("测试3: 高通滤波器\n");
    GenerateTestFilterData(8000.0f, 1.0f, 1);
    FilterCharacteristics result3 = AnalyzeFilterFromSweep();
    PrintFilterCharacteristics(&result3);
    
    printf("验证完成！\n");
    return 0;
}
