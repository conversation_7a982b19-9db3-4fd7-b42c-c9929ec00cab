#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 优化内存使用
#define ADC1_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 优化内存使用
#define ADC2_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1幅度（滤波器输出）
    float adc2_amplitude;   // ADC2幅度（滤波器输入）
    float magnitude_db;     // 幅度响应(dB)
    float phase_deg;        // 相位响应(度)
} FrequencyResponse;

// 滤波器特性分析结构体
typedef struct {
    float center_frequency;     // 中心频率 (Hz)
    float cutoff_frequency;     // 截止频率 (Hz)
    float q_factor;             // Q值
    float bandwidth;            // 带宽 (Hz)
    float max_gain_db;          // 最大增益 (dB)
    float gain_at_cutoff_db;    // 截止频率处增益 (dB)
    uint8_t filter_type;        // 滤波器类型: 0=低通, 1=高通, 2=带通, 3=带阻, 4=未知
} FilterCharacteristics;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_BUFFER_SIZE 50  // 只保存最近50个点的结果用于分析
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 减少内存使用：50×20字节=1KB
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=寻找最大值，1=归一化输出

// 滤波器特性分析变量
FilterCharacteristics current_filter;     // 当前滤波器特性
volatile uint8_t filter_analysis_complete = 0;  // 滤波器分析完成标志
volatile uint8_t display_filter_info = 0;       // 显示滤波器信息标志

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void AnalyzeFilterCharacteristics(void);

// 滤波器分析函数声明
FilterCharacteristics AnalyzeFilterFromSweep(void);
void DisplayFilterCharacteristics(FilterCharacteristics* filter_char);
float FindCutoffFrequency(float max_gain_db);
float CalculateQFactor(float center_freq, float bandwidth);
uint8_t DetermineFilterType(void);
void GenerateTestFilterData(void);  // 生成测试滤波器数据

// 扫频测试函数声明（简化版）

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义八个按钮 - 更大尺寸便于操作
Button_t buttons[8] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY},     // 扫频测试按钮
    {290, 200, 90, 60, "FILTER",   0.0f,     CYAN}      // 滤波器信息按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 8; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 8; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 8;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 7) {
                    // 滤波器信息按钮
                    if (filter_analysis_complete) {
                        // 显示现有的滤波器分析结果
                        display_filter_info = 1;

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Filter Info Displayed");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, BLUE);
                    } else {
                        // 生成测试数据并分析
                        GenerateTestFilterData();
                        current_filter = AnalyzeFilterFromSweep();
                        filter_analysis_complete = 1;
                        display_filter_info = 1;

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Test filter analyzed!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, GREEN);
                    }

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                if (current_sweep_point < SWEEP_POINTS)
                {
                    // 设置下一个频率
                    float next_freq = 1000.0f + current_sweep_point * 200.0f;
                    AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                    // 显示进度
                    char progress_info[100];
                    sprintf(progress_info, "Sweep: %d/%d (%.1fkHz)",
                            current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                    lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，开始第二阶段
                        sweep_phase = 1;
                        current_sweep_point = 0;

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: Normalized Output", BLUE);
                    } else {
                        // 第二阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        // 分析滤波器特性
                        current_filter = AnalyzeFilterFromSweep();
                        filter_analysis_complete = 1;
                        display_filter_info = 1;

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Complete!", GREEN);
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        // 显示滤波器特性信息
        if (display_filter_info && filter_analysis_complete) {
            DisplayFilterCharacteristics(&current_filter);
            display_filter_info = 0;  // 清除显示标志
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：寻找最大值

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);

    // 开始扫频测试，无串口输出

    // 等待频率稳定
    delay_ms(10);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 计算ADC1和ADC2的RMS值（去除直流分量）
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 计算直流分量
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_dc += adc1_sample_buffer[i];
        adc2_dc += adc2_sample_buffer[i];
    }
    adc1_dc /= ADC1_SAMPLE_SIZE;
    adc2_dc /= ADC2_SAMPLE_SIZE;

    // 计算RMS值（去除直流分量）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_ac = adc1_sample_buffer[i] - adc1_dc;
        float adc2_ac = adc2_sample_buffer[i] - adc2_dc;
        adc1_rms += adc1_ac * adc1_ac;
        adc2_rms += adc2_ac * adc2_ac;
    }
    adc1_rms = sqrtf(adc1_rms / ADC1_SAMPLE_SIZE);
    adc2_rms = sqrtf(adc2_rms / ADC2_SAMPLE_SIZE);

    // 计算幅度响应 (dB)
    float magnitude_db;
    if (adc2_rms > 0 && adc1_rms > 0) {
        magnitude_db = 20.0f * log10f(adc1_rms / adc2_rms);
    } else {
        magnitude_db = -100.0f; // 很小的值
    }

    // 简化的相位计算（基于峰值位置差）
    int adc1_peak_idx = 0, adc2_peak_idx = 0;
    float adc1_max = 0, adc2_max = 0;

    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_val = fabsf(adc1_sample_buffer[i] - adc1_dc);
        float adc2_val = fabsf(adc2_sample_buffer[i] - adc2_dc);

        if (adc1_val > adc1_max) {
            adc1_max = adc1_val;
            adc1_peak_idx = i;
        }
        if (adc2_val > adc2_max) {
            adc2_max = adc2_val;
            adc2_peak_idx = i;
        }
    }

    // 计算相位差（度）
    float sample_period = 1.0f / 815534.0f; // 采样周期
    float signal_period = 1.0f / current_freq; // 信号周期
    float time_diff = (adc1_peak_idx - adc2_peak_idx) * sample_period;
    float phase_deg = (time_diff / signal_period) * 360.0f;

    // 限制相位范围到 -180 到 +180 度
    while (phase_deg > 180.0f) {
        phase_deg -= 360.0f;
    }
    while (phase_deg < -180.0f) {
        phase_deg += 360.0f;
    }

    // 计算电压幅度比（线性比值，不是dB）
    float voltage_ratio = 0.0f;
    if (adc2_rms > 0) {
        voltage_ratio = adc1_rms / adc2_rms;
    }

    if (sweep_phase == 0) {
        // 第一阶段：寻找最大值，不输出
        if (voltage_ratio > max_voltage_ratio) {
            max_voltage_ratio = voltage_ratio;
        }
    } else {
        // 第二阶段：归一化输出
        float normalized_ratio = 0.0f;
        float normalized_ratio_inverse = 0.0f;

        if (max_voltage_ratio > 0.0f) {
            normalized_ratio = voltage_ratio / max_voltage_ratio;
            if (normalized_ratio > 0) {
                normalized_ratio_inverse = 1.0f / normalized_ratio;
            }
        }

        // 输出频率、归一化电压幅度比、归一化电压幅度比的倒数
        printf("%.1f\t%.6f\t%.6f\r\n", current_freq, normalized_ratio, normalized_ratio_inverse);
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_rms;
    sweep_results[buffer_idx].adc2_amplitude = adc2_rms;
    sweep_results[buffer_idx].magnitude_db = magnitude_db;
    sweep_results[buffer_idx].phase_deg = phase_deg;

    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}

void AnalyzeFilterCharacteristics(void)
{
    printf("=== FILTER ANALYSIS (Based on Recent Data) ===\r\n");

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        printf("Insufficient data for analysis\r\n");
        printf("=== END OF ANALYSIS ===\r\n");
        return;
    }

    // 找到最大和最小幅度
    float max_magnitude = -100.0f;
    float min_magnitude = 100.0f;
    float max_freq = 0, min_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
            if (sweep_results[i].magnitude_db < min_magnitude) {
                min_magnitude = sweep_results[i].magnitude_db;
                min_freq = sweep_results[i].frequency;
            }
        }
    }

    printf("Recent Data Analysis:\r\n");
    printf("Max Gain: %.2f dB at %.1f Hz\r\n", max_magnitude, max_freq);
    printf("Min Gain: %.2f dB at %.1f Hz\r\n", min_magnitude, min_freq);
    printf("Gain Range: %.2f dB\r\n", max_magnitude - min_magnitude);

    // 简单的滤波器类型判断
    float gain_variation = max_magnitude - min_magnitude;
    if (gain_variation > 10.0f) {
        printf("Filter Type: Significant frequency response variation detected\r\n");
    } else if (max_magnitude > -1.0f) {
        printf("Filter Type: Likely passband region\r\n");
    } else if (max_magnitude < -10.0f) {
        printf("Filter Type: Likely stopband region\r\n");
    } else {
        printf("Filter Type: Transition region\r\n");
    }

    printf("Note: Complete analysis requires full sweep data\r\n");
    printf("=== END OF ANALYSIS ===\r\n");
}

/**
 * @brief 从扫频数据分析滤波器特性
 * @retval FilterCharacteristics 滤波器特性结构体
 */
FilterCharacteristics AnalyzeFilterFromSweep(void)
{
    FilterCharacteristics filter_char = {0};

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        // 数据不足，返回默认值
        filter_char.filter_type = 4; // 未知类型
        return filter_char;
    }

    // 找到最大增益和对应频率
    float max_magnitude = -100.0f;
    float max_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
        }
    }

    filter_char.max_gain_db = max_magnitude;
    filter_char.center_frequency = max_freq;

    // 计算归一化幅度比和-3dB截止频率
    // 首先找到最大幅度比作为归一化基准
    float max_amplitude_ratio = 0.0f;
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float amplitude_ratio = 0.0f;
            if (sweep_results[i].adc2_amplitude > 0) {
                amplitude_ratio = sweep_results[i].adc1_amplitude / sweep_results[i].adc2_amplitude;
            }
            if (amplitude_ratio > max_amplitude_ratio) {
                max_amplitude_ratio = amplitude_ratio;
            }
        }
    }

    // 计算-3dB点对应的归一化幅度比 (0.707)
    float cutoff_ratio = max_amplitude_ratio * 0.707f;  // -3dB点

    // 寻找截止频率和带宽
    float lower_freq = 0, upper_freq = 0;
    int found_lower = 0, found_upper = 0;
    float closest_cutoff_freq = 0;
    float min_cutoff_diff = 1000.0f;

    // 搜索接近0.707倍最大值的频率点
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float amplitude_ratio = 0.0f;
            if (sweep_results[i].adc2_amplitude > 0) {
                amplitude_ratio = sweep_results[i].adc1_amplitude / sweep_results[i].adc2_amplitude;
            }

            // 计算与截止比值的差异
            float cutoff_diff = fabsf(amplitude_ratio - cutoff_ratio);

            // 记录最接近截止比值的频率作为截止频率
            if (cutoff_diff < min_cutoff_diff) {
                min_cutoff_diff = cutoff_diff;
                closest_cutoff_freq = sweep_results[i].frequency;
            }

            // 寻找3dB带宽边界（幅度比大于0.707的范围）
            if (amplitude_ratio >= cutoff_ratio) {
                if (!found_lower || sweep_results[i].frequency < lower_freq) {
                    lower_freq = sweep_results[i].frequency;
                    found_lower = 1;
                }
                if (!found_upper || sweep_results[i].frequency > upper_freq) {
                    upper_freq = sweep_results[i].frequency;
                    found_upper = 1;
                }
            }
        }
    }

    filter_char.cutoff_frequency = closest_cutoff_freq;
    filter_char.gain_at_cutoff_db = 20.0f * log10f(0.707f);  // -3dB

    // 计算3dB带宽
    if (found_lower && found_upper) {
        filter_char.bandwidth = upper_freq - lower_freq;
    } else if (found_lower || found_upper) {
        // 只找到一边，估算带宽
        filter_char.bandwidth = fabsf(max_freq - (found_lower ? lower_freq : upper_freq)) * 2;
    } else {
        // 都没找到，使用默认带宽
        filter_char.bandwidth = max_freq * 0.1f; // 假设10%带宽
    }

    // 计算Q值
    filter_char.q_factor = CalculateQFactor(filter_char.center_frequency, filter_char.bandwidth);

    // 判断滤波器类型
    filter_char.filter_type = DetermineFilterType();

    return filter_char;
}

/**
 * @brief 查找截止频率
 * @param cutoff_ratio 截止幅度比 (归一化值，通常为0.707)
 * @retval 截止频率 (Hz)
 */
float FindCutoffFrequency(float cutoff_ratio)
{
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    float closest_freq = 0;
    float min_diff = 1000.0f; // 很大的初始值

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float amplitude_ratio = 0.0f;
            if (sweep_results[i].adc2_amplitude > 0) {
                amplitude_ratio = sweep_results[i].adc1_amplitude / sweep_results[i].adc2_amplitude;
            }

            float diff = fabsf(amplitude_ratio - cutoff_ratio);
            if (diff < min_diff) {
                min_diff = diff;
                closest_freq = sweep_results[i].frequency;
            }
        }
    }

    return closest_freq;
}

/**
 * @brief 计算Q值
 * @param center_freq 中心频率 (Hz)
 * @param bandwidth 带宽 (Hz)
 * @retval Q值
 */
float CalculateQFactor(float center_freq, float bandwidth)
{
    if (bandwidth > 0) {
        return center_freq / bandwidth;
    }
    return 0.0f;
}

/**
 * @brief 判断滤波器类型
 * @retval 滤波器类型: 0=低通, 1=高通, 2=带通, 3=带阻, 4=未知
 * @note 判断标准：
 *       低通：1kHz和1.2kHz均>0.7，400kHz和400.2kHz均<0.7
 *       高通：1kHz和1.2kHz均<0.7，400kHz和400.2kHz均>0.7
 *       带通：1kHz和1.2kHz均<0.7，400kHz和400.2kHz均<0.7
 *       带阻：1kHz和1.2kHz均>0.7，400kHz和400.2kHz均>0.7
 */
uint8_t DetermineFilterType(void)
{
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        return 4; // 未知
    }

    // 首先计算最大幅度比作为归一化基准
    float max_amplitude_ratio = 0.0f;
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float amplitude_ratio = 0.0f;
            if (sweep_results[i].adc2_amplitude > 0) {
                amplitude_ratio = sweep_results[i].adc1_amplitude / sweep_results[i].adc2_amplitude;
            }
            if (amplitude_ratio > max_amplitude_ratio) {
                max_amplitude_ratio = amplitude_ratio;
            }
        }
    }

    // 查找特定频率点的归一化幅度比
    float ratio_1k = 0, ratio_1k2 = 0, ratio_400k = 0, ratio_400k2 = 0;
    int found_1k = 0, found_1k2 = 0, found_400k = 0, found_400k2 = 0;

    // 定义频率容差（Hz）
    float freq_tolerance = 50.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float freq = sweep_results[i].frequency;
            float amplitude_ratio = 0.0f;
            if (sweep_results[i].adc2_amplitude > 0) {
                amplitude_ratio = sweep_results[i].adc1_amplitude / sweep_results[i].adc2_amplitude;
            }
            // 归一化到最大值
            float normalized_ratio = amplitude_ratio / max_amplitude_ratio;

            // 查找1kHz附近的点
            if (fabsf(freq - 1000.0f) < freq_tolerance) {
                ratio_1k = normalized_ratio;
                found_1k = 1;
            }
            // 查找1.2kHz附近的点
            else if (fabsf(freq - 1200.0f) < freq_tolerance) {
                ratio_1k2 = normalized_ratio;
                found_1k2 = 1;
            }
            // 查找400kHz附近的点
            else if (fabsf(freq - 400000.0f) < freq_tolerance) {
                ratio_400k = normalized_ratio;
                found_400k = 1;
            }
            // 查找400.2kHz附近的点
            else if (fabsf(freq - 400200.0f) < freq_tolerance) {
                ratio_400k2 = normalized_ratio;
                found_400k2 = 1;
            }
        }
    }

    // 如果没有找到所有需要的频率点，返回未知
    if (!found_1k || !found_1k2 || !found_400k || !found_400k2) {
        return 4; // 未知
    }

    // 根据标准判断滤波器类型
    int low_freq_high = (ratio_1k > 0.7f) && (ratio_1k2 > 0.7f);      // 低频段>0.7
    int low_freq_low = (ratio_1k < 0.7f) && (ratio_1k2 < 0.7f);       // 低频段<0.7
    int high_freq_high = (ratio_400k > 0.7f) && (ratio_400k2 > 0.7f); // 高频段>0.7
    int high_freq_low = (ratio_400k < 0.7f) && (ratio_400k2 < 0.7f);  // 高频段<0.7

    if (low_freq_high && high_freq_low) {
        return 0; // 低通滤波器
    } else if (low_freq_low && high_freq_high) {
        return 1; // 高通滤波器
    } else if (low_freq_low && high_freq_low) {
        return 2; // 带通滤波器
    } else if (low_freq_high && high_freq_high) {
        return 3; // 带阻滤波器
    }

    return 4; // 未知类型
}

/**
 * @brief 在LCD上显示滤波器特性
 * @param filter_char 滤波器特性结构体指针
 */
void DisplayFilterCharacteristics(FilterCharacteristics* filter_char)
{
    char display_buffer[100];
    uint16_t y_pos = 290;  // 起始Y位置
    uint16_t line_height = 15;  // 行高

    // 清除显示区域
    lcd_fill(0, y_pos, lcddev.width, lcddev.height, WHITE);

    // 显示滤波器类型
    const char* filter_types[] = {"Low-Pass", "High-Pass", "Band-Pass", "Band-Stop", "Unknown"};
    sprintf(display_buffer, "Type: %s", filter_types[filter_char->filter_type]);
    lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, BLUE);
    y_pos += line_height;

    // 显示中心频率
    if (filter_char->center_frequency >= 1000.0f) {
        sprintf(display_buffer, "Fc: %.1f kHz", filter_char->center_frequency / 1000.0f);
    } else {
        sprintf(display_buffer, "Fc: %.0f Hz", filter_char->center_frequency);
    }
    lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, BLACK);
    y_pos += line_height;

    // 显示截止频率（-3dB点，幅度比=0.707处）
    if (filter_char->cutoff_frequency > 0) {
        if (filter_char->cutoff_frequency >= 1000.0f) {
            sprintf(display_buffer, "F-3dB: %.1f kHz", filter_char->cutoff_frequency / 1000.0f);
        } else {
            sprintf(display_buffer, "F-3dB: %.0f Hz", filter_char->cutoff_frequency);
        }
        lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, BLACK);
        y_pos += line_height;
    }

    // 显示Q值
    sprintf(display_buffer, "Q: %.2f", filter_char->q_factor);
    lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, RED);
    y_pos += line_height;

    // 显示3dB带宽（幅度比>0.707的频率范围）
    if (filter_char->bandwidth >= 1000.0f) {
        sprintf(display_buffer, "BW3dB: %.1f kHz", filter_char->bandwidth / 1000.0f);
    } else {
        sprintf(display_buffer, "BW3dB: %.0f Hz", filter_char->bandwidth);
    }
    lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, GREEN);
    y_pos += line_height;

    // 显示最大增益
    sprintf(display_buffer, "Gain: %.1f dB", filter_char->max_gain_db);
    lcd_show_string(5, y_pos, lcddev.width, line_height, 12, display_buffer, MAGENTA);
}

/**
 * @brief 生成测试滤波器数据（用于演示）
 * @note 模拟一个中心频率为10kHz，Q值为5的带通滤波器
 */
void GenerateTestFilterData(void)
{
    // 清空现有数据
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 模拟带通滤波器响应
    float center_freq = 10000.0f;  // 10kHz中心频率
    float q_factor = 5.0f;         // Q值为5
    float bandwidth = center_freq / q_factor;  // 2kHz带宽

    // 生成包含关键频率点的测试数据
    // 确保包含1kHz, 1.2kHz, 400kHz, 400.2kHz这些关键频率点
    float test_frequencies[SWEEP_BUFFER_SIZE];

    // 前几个点设置为关键频率
    test_frequencies[0] = 1000.0f;    // 1kHz
    test_frequencies[1] = 1200.0f;    // 1.2kHz
    test_frequencies[2] = 400000.0f;  // 400kHz
    test_frequencies[3] = 400200.0f;  // 400.2kHz

    // 其余点按原来的方式分布
    for (int i = 4; i < SWEEP_BUFFER_SIZE; i++) {
        test_frequencies[i] = 2000.0f + (i-4) * 8000.0f;  // 2kHz到368kHz
    }

    // 生成频率响应数据
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        float freq = test_frequencies[i];
        sweep_results[i].frequency = freq;

        // 模拟带通滤波器的幅度响应
        float freq_ratio = freq / center_freq;
        float q_term = q_factor * (freq_ratio - 1.0f / freq_ratio);
        float magnitude_linear = 1.0f / sqrtf(1.0f + q_term * q_term);  // 归一化幅度比
        float magnitude_db = 20.0f * log10f(magnitude_linear);

        sweep_results[i].magnitude_db = magnitude_db;

        // 模拟相位响应
        sweep_results[i].phase_deg = -atanf(q_term) * 180.0f / PI;

        // 模拟幅度值 - 重要：这里要确保幅度比正确
        // ADC2作为输入参考，保持恒定
        sweep_results[i].adc2_amplitude = 1000.0f;  // 输入恒定1000
        // ADC1作为滤波器输出，按照滤波器响应变化
        sweep_results[i].adc1_amplitude = magnitude_linear * 1000.0f;  // 输出 = 输入 × 滤波器增益

        // 验证：在中心频率处，magnitude_linear应该接近1.0，即adc1/adc2 ≈ 1.0
        // 在截止频率处，magnitude_linear应该接近0.707，即adc1/adc2 ≈ 0.707
    }

    total_sweep_points = SWEEP_BUFFER_SIZE;
    filter_analysis_complete = 0;  // 重置分析标志，需要重新分析

    // 显示测试数据生成完成
    lcd_show_string(10, 70, lcddev.width, 20, 12, "Test data generated!", GREEN);
}