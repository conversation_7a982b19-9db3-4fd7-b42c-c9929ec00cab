# 滤波器特性分析功能说明

## 功能概述

本项目新增了滤波器特性分析功能，可以通过扫频测试自动分析滤波器的关键参数并在LCD屏幕上显示。

## 主要功能

### 1. 滤波器参数计算
- **Q值 (Quality Factor)**: 滤波器的品质因数，Q = fc/BW
- **中心频率 (Center Frequency)**: 滤波器的中心工作频率
- **截止频率 (Cutoff Frequency)**: -3dB衰减点对应的频率
- **带宽 (Bandwidth)**: -3dB带宽
- **最大增益**: 滤波器的最大增益值(dB)

### 2. 滤波器类型识别
自动识别滤波器类型：
- 低通滤波器 (Low-Pass)
- 高通滤波器 (High-Pass) 
- 带通滤波器 (Band-Pass)
- 带阻滤波器 (Band-Stop)
- 未知类型 (Unknown)

### 3. LCD显示功能
在LCD屏幕下方显示滤波器特性信息：
- 滤波器类型
- 中心频率 (Fc)
- 截止频率 (F3dB)
- Q值
- 带宽 (BW)
- 最大增益

## 操作方法

### 方法一：扫频测试后自动分析
1. 按PE4键选择"SWEEP OFF"按钮
2. 按PE3键启动扫频测试
3. 等待扫频完成，系统会自动分析滤波器特性
4. 分析结果会自动显示在LCD屏幕下方

### 方法二：手动查看滤波器信息
1. 按PE4键选择"FILTER"按钮
2. 按PE3键执行以下操作：
   - 如果已有扫频数据：显示滤波器分析结果
   - 如果没有数据：生成测试数据并显示分析结果

## 测试数据

当没有实际扫频数据时，系统会生成一个模拟的带通滤波器测试数据：
- 中心频率：10kHz
- Q值：5
- 带宽：2kHz
- 类型：带通滤波器

## 技术实现

### 核心算法
1. **幅度响应分析**: 通过ADC1和ADC2的RMS值计算增益
2. **-3dB点检测**: 寻找最大增益-3dB处的频率点
3. **带宽计算**: 计算上下-3dB点之间的频率差
4. **Q值计算**: Q = 中心频率 / 带宽
5. **滤波器类型判断**: 基于低、中、高频段的增益分布

### 数据结构
```c
typedef struct {
    float center_frequency;     // 中心频率 (Hz)
    float cutoff_frequency;     // 截止频率 (Hz) 
    float q_factor;             // Q值
    float bandwidth;            // 带宽 (Hz)
    float max_gain_db;          // 最大增益 (dB)
    float gain_at_cutoff_db;    // 截止频率处增益 (dB)
    uint8_t filter_type;        // 滤波器类型
} FilterCharacteristics;
```

## 按钮布局

```
+----------+----------+----------+----------+
| +100kHz  | +10kHz   | +1kHz    | +100Hz   |
+----------+----------+----------+----------+
| DAC OFF  | DAC x1.0 | SWEEP OFF| FILTER   |
+----------+----------+----------+----------+
```

## 显示示例

```
Type: Band-Pass
Fc: 10.0 kHz
F3dB: 9.0 kHz  
Q: 5.00
BW: 2.0 kHz
Gain: 0.0 dB
```

## 注意事项

1. 扫频测试需要连接被测滤波器电路
2. ADC1连接滤波器输出，ADC2连接滤波器输入
3. 扫频范围：1kHz - 400kHz，步进200Hz
4. 分析精度取决于扫频点数和信号质量
5. 建议在稳定的测试环境下进行测量

## 文件修改

主要修改的文件：
- `USER/main.c`: 添加滤波器分析功能
- 新增函数：
  - `AnalyzeFilterFromSweep()`: 主分析函数
  - `DisplayFilterCharacteristics()`: LCD显示函数
  - `FindCutoffFrequency()`: 截止频率查找
  - `CalculateQFactor()`: Q值计算
  - `DetermineFilterType()`: 滤波器类型判断
  - `GenerateTestFilterData()`: 测试数据生成
